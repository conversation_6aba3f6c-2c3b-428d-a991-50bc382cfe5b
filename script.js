$(document).ready(function() {
    // Mise à jour du slider en temps réel
    $('#depth').on('input', function() {
        $('.range-value').text($(this).val() + '%');
    });

    // Soumission du formulaire
    $('#statsForm').on('submit', function(e) {
        e.preventDefault();
        calculateStats();
    });

    function calculateStats() {
        // Récupération des valeurs
        const length = parseFloat($('#length').val());
        const depth = parseFloat($('#depth').val()) / 100;
        const frequency = parseFloat($('#frequency').val());
        const duration = parseFloat($('#duration').val());
        const period = parseFloat($('#period').val());

        // Calculs humoristiques
        const effectiveLength = length * depth;
        const sessionsPerMonth = frequency * 4.33; // 4.33 semaines par mois
        const totalSessions = sessionsPerMonth * period;
        const totalDistance = (effectiveLength * totalSessions * 60) / 100; // en mètres
        const totalTimeHours = (totalSessions * duration) / 60;
        const calories = totalSessions * duration * 4.2; // ~4.2 cal/min

        // Affichage des résultats
        $('#totalDistance').text(totalDistance.toFixed(1));
        $('#calories').text(Math.round(calories));
        $('#totalTime').text(totalTimeHours.toFixed(1));

        // Calcul du niveau
        const level = calculateLevel(frequency, duration, period);
        $('#level').text(level.name);
        $('#levelDesc').text(level.description);

        // Faits amusants
        generateFunFacts(totalDistance, totalSessions, calories, totalTimeHours);

        // Comparaisons
        generateComparisons(totalDistance, totalTimeHours, calories);

        // Graphique
        createChart(frequency, duration, totalSessions, period);

        // Afficher les résultats
        $('#results').fadeIn(500);
        
        // Scroll vers les résultats
        $('html, body').animate({
            scrollTop: $('#results').offset().top - 20
        }, 800);
    }

    function calculateLevel(frequency, duration, period) {
        const score = frequency * duration * Math.log(period + 1);
        
        if (score < 10) return { name: "🐣", description: "Débutant Timide" };
        if (score < 25) return { name: "🌱", description: "Amateur Motivé" };
        if (score < 50) return { name: "🔥", description: "Confirmé Passionné" };
        if (score < 100) return { name: "🏆", description: "Expert Endurant" };
        if (score < 200) return { name: "👑", description: "Maître Légendaire" };
        return { name: "🚀", description: "Dieu du Sexe" };
    }

    function generateFunFacts(distance, sessions, calories, hours) {
        const facts = [
            `🏃‍♂️ Vous avez parcouru l'équivalent de ${(distance/1000).toFixed(2)} km !`,
            `🍕 Vous avez brûlé l'équivalent de ${Math.round(calories/285)} pizzas !`,
            `📺 Vous avez passé ${hours.toFixed(1)} heures, soit ${Math.round(hours/24)} jours complets !`,
            `🎯 Vous avez eu ${Math.round(sessions)} sessions au total !`,
            `⚡ Votre vitesse moyenne était de ${(distance/hours/1000).toFixed(2)} km/h !`
        ];

        // Faits bonus selon les valeurs
        if (distance > 1000) {
            facts.push("🏔️ Vous pourriez avoir gravi l'Everest avec cette distance !");
        }
        if (hours > 100) {
            facts.push("📚 Vous auriez pu apprendre une nouvelle langue avec ce temps !");
        }
        if (sessions > 365) {
            facts.push("📅 Plus d'une fois par jour en moyenne, impressionnant !");
        }

        $('#funFactsList').empty();
        facts.forEach(fact => {
            $('#funFactsList').append(`<li>${fact}</li>`);
        });
    }

    function generateComparisons(distance, hours, calories) {
        const comparisons = [
            `🚶‍♂️ Distance = ${Math.round(distance/1.4)} pas de marche normale`,
            `🏊‍♂️ Temps = ${Math.round(hours*2)} longueurs de piscine`,
            `🍔 Calories = ${Math.round(calories/540)} Big Mac`,
            `🎬 Temps = ${Math.round(hours/2)} films complets`,
            `☕ Calories = ${Math.round(calories/2)} tasses de café`
        ];

        $('#comparisons').empty();
        comparisons.forEach(comp => {
            $('#comparisons').append(`<div class="comparison-item">${comp}</div>`);
        });
    }

    function createChart(frequency, duration, sessions, period) {
        const ctx = document.getElementById('performanceChart').getContext('2d');
        
        // Données simulées pour le graphique
        const months = [];
        const data = [];
        
        for (let i = 0; i < Math.min(period, 12); i++) {
            months.push(`Mois ${i + 1}`);
            // Simulation de variation réaliste
            const variation = 0.8 + Math.random() * 0.4; // Entre 80% et 120%
            data.push(Math.round(frequency * 4.33 * variation));
        }

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Sessions par mois',
                    data: data,
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '📈 Évolution de vos Performances',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Nombre de sessions'
                        }
                    }
                }
            }
        });
    }

    // Animations et effets
    $('.stat-card').hover(
        function() {
            $(this).css('transform', 'translateY(-5px) scale(1.02)');
        },
        function() {
            $(this).css('transform', 'translateY(0) scale(1)');
        }
    );

    // Easter eggs
    let clickCount = 0;
    $('header h1').click(function() {
        clickCount++;
        if (clickCount === 5) {
            $(this).text('🍆💦 CALCULATEUR ULTIME 💦🍆');
            $('body').css('background', 'linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3)');
            clickCount = 0;
        }
    });
});
