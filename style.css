* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

header h1 {
    font-size: 2.5em;
    color: #e74c3c;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.2em;
    color: #666;
    font-style: italic;
}

.form-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    margin-bottom: 30px;
}

.form-section h2 {
    color: #8e44ad;
    margin-bottom: 25px;
    text-align: center;
    font-size: 1.8em;
}

.input-group {
    margin-bottom: 20px;
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #555;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.2);
}

.input-group input[type="range"] {
    -webkit-appearance: none;
    height: 8px;
    background: linear-gradient(to right, #e74c3c, #f39c12);
    border-radius: 5px;
}

.input-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: #e74c3c;
    border-radius: 50%;
    cursor: pointer;
}

.range-value {
    position: absolute;
    right: 50px;
    top: 35px;
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: bold;
}

.emoji {
    position: absolute;
    right: 15px;
    top: 35px;
    font-size: 1.5em;
}

.calculate-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin-top: 20px;
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.results-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    margin-bottom: 30px;
}

.results-section h2 {
    color: #27ae60;
    margin-bottom: 25px;
    text-align: center;
    font-size: 1.8em;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 1.1em;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-value {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-unit {
    font-size: 0.9em;
    opacity: 0.8;
}

.fun-facts {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.fun-facts h3 {
    color: #e74c3c;
    margin-bottom: 15px;
    text-align: center;
}

.fun-facts ul {
    list-style: none;
}

.fun-facts li {
    background: white;
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.comparison {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}

.comparison h3 {
    color: #8e44ad;
    margin-bottom: 15px;
    text-align: center;
}

.comparison-item {
    background: white;
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #8e44ad;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

footer {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    margin-top: 20px;
}

footer p {
    color: #666;
    font-style: italic;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-value {
        font-size: 2em;
    }
}
